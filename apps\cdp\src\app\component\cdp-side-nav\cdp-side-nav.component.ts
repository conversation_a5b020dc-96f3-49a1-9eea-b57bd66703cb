import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterOutlet } from '@angular/router';
import { CdpNavigationService } from '../../service/cdp-navigation.service';
import { UnicaSideNavElementItemConfig } from '@hcl/unica-common';
import { CdpUserService } from '../../service/cdp-user.service';
import { LetDirective } from '@ngrx/component';
import { UnicaSideNavContainerComponent } from '@hcl/angular/unica-side-nav';

@Component({
  selector: 'cdp-side-nav',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    LetDirective,
    UnicaSideNavContainerComponent
  ],
  providers: [
    CdpNavigationService
  ],
  templateUrl: './cdp-side-nav.component.html',
  styleUrl: './cdp-side-nav.component.scss',
})
export class CdpSideNavComponent {

  /**
   * Default constructor
   * @param userService
   */
  constructor(protected navService: CdpNavigationService,
              protected userService: CdpUserService,
              private router: Router) {
  }

  protected readonly navigator = navigator;

  /**
   * Navigate the user
   * @param $event
   */
  navigate(event: UnicaSideNavElementItemConfig[]) {
    const path: string[] = ['dataplatform']
    event.forEach((e) => {
      if (typeof e.path === 'string') {
        path.push(e.path);
      } else {
        e.path?.forEach((p) => path.push(p))
      }
    });
    this.router.navigate(path);
  }
}
