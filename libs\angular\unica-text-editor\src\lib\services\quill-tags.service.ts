import { Injectable } from '@angular/core';
import { QuillToolbarConfig } from 'ngx-quill';
// Import Quill from centralized loader
import { QuillInstance } from '../core/quill-loader';
export interface TagSuggestion {
  id: string;
  display: string;
}

export interface TagsConfig {
  suggestions: TagSuggestion[];
  onTagSelected?: (tag: TagSuggestion) => void;
  insertFormat?: 'textblock' | 'text';
  addSpace?: boolean;
}

@Injectable()
export class QuillTagsService {
  private editorInstance: QuillInstance | null = null;
  private config: TagsConfig = {
    suggestions: [],
    insertFormat: 'textblock',
    addSpace: true,
  };

  /**
   * Initialize the Tags service with configuration
   */
  initialize(config: TagsConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Set the Quill editor instance
   */
  setEditor(editor: QuillInstance): void {
    this.editorInstance = editor;
    this.initializeTagsPickerItems();
  }

  /**
   * Add Tags picker to Quill toolbar configuration
   * This is the main method to integrate Tags into any Quill toolbar
   */
  addTagsToToolbar(container: QuillToolbarConfig): QuillToolbarConfig {
    if (this.config.suggestions.length === 0) {
      return container;
    }

    // Create tags array for picker options
    const tagsArray = this.config.suggestions.map(
      (suggestion) => suggestion.display,
    );

    // Add tags picker to toolbar (before the last group)
    container.splice(-1, 0, [{ tags: tagsArray }]);

    return container;
  }

  /**
   * Get toolbar handlers for Tags functionality
   */
  getToolbarHandlers(): { [key: string]: (value: string) => void } {
    return {
      tags: this.tagsHandler.bind(this),
    };
  }

  /**
   * Handler for tags picker in toolbar
   */
  private tagsHandler(selectedTag: string): void {
    if (!this.editorInstance || !selectedTag) {
      return;
    }

    // Get current cursor position
    const selection = this.editorInstance.getSelection();
    if (!selection) {
      return;
    }

    // Find the suggestion item by display name
    const selectedSuggestion = this.config.suggestions.find(
      (s) => s.display === selectedTag,
    );

    if (!selectedSuggestion) {
      return;
    }

    // Insert the selected tag
    this.insertTagAtCursor(selectedSuggestion, selection);

    // Call callback if provided
    if (this.config.onTagSelected) {
      this.config.onTagSelected(selectedSuggestion);
    }
  }

  /**
   * Insert selected tag at current cursor position
   * Handles both insertion (no selection) and replacement (text selected)
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private insertTagAtCursor(suggestion: TagSuggestion, selection: any): void {
    if (!this.editorInstance) return;

    const newLabel = `<--${suggestion.display}-->`;

    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const delta: any[] = [];

      // Retain content before selection
      if (selection.index > 0) {
        delta.push({ retain: selection.index });
      }

      // If text is selected, delete it first (replace behavior)
      if (selection.length > 0) {
        delta.push({ delete: selection.length });
      }

      // Insert based on format type
      if (this.config.insertFormat === 'textblock') {
        // Insert as text block (custom blot)
        delta.push({
          insert: {
            editortextblock: {
              label: newLabel,
              id: suggestion.id,
              length: newLabel.length,
            },
          },
        });
      } else {
        // Insert as plain text
        delta.push({ insert: newLabel });
      }

      // Add space after the tag if configured
      if (this.config.addSpace) {
        delta.push({ insert: ' ' });
      }

      // Apply the delta
      this.editorInstance.updateContents(delta, 'user');

      // Set cursor position after the inserted content
      const newPosition = selection.index + (this.config.addSpace ? 2 : 1);
      this.editorInstance.setSelection(newPosition, 0);
    } catch (error) {
      console.error('QuillTagsService: Error inserting tag:', error);
    }
  }

  /**
   * Initialize tags picker items with proper text content
   * This fixes the issue where Quill picker items are empty
   */
  private initializeTagsPickerItems(): void {
    // Use setTimeout to ensure DOM is ready
    setTimeout(() => {
      const pickerItems = document.querySelectorAll(
        '.ql-picker.ql-tags .ql-picker-item',
      );
      pickerItems.forEach((item: Element) => {
        const htmlItem = item as HTMLElement;
        const dataValue = htmlItem.getAttribute('data-value');
        if (dataValue && !htmlItem.textContent) {
          htmlItem.textContent = dataValue;
        }
      });
    }, 100);
  }

  /**
   * Update suggestions dynamically
   */
  updateSuggestions(suggestions: TagSuggestion[]): void {
    this.config.suggestions = suggestions;
    // Re-initialize picker items if editor is available
    if (this.editorInstance) {
      this.initializeTagsPickerItems();
    }
  }

  /**
   * Get current suggestions
   */
  getSuggestions(): TagSuggestion[] {
    return this.config.suggestions;
  }

  /**
   * Programmatically insert a tag (useful for external triggers)
   */
  insertTag(suggestion: TagSuggestion): void {
    if (!this.editorInstance) {
      return;
    }

    const selection = this.editorInstance.getSelection() || {
      index: this.editorInstance.getLength() - 1,
      length: 0,
    };
    this.insertTagAtCursor(suggestion, selection);

    // Call callback if provided
    if (this.config.onTagSelected) {
      this.config.onTagSelected(suggestion);
    }
  }
}
