import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  RouterModule
} from '@angular/router';

import { RemoteEntryComponent } from './entry.component';
import { AngularLibHomeComponent } from '../component/angular-lib-home/angular-lib-home.component';
import { appRoutes } from '../angular-lib.routes';
import { UnicaTextEditorComponent } from '@hcl/angular/unica-text-editor';

@NgModule({
  declarations: [
    RemoteEntryComponent,
    AngularLibHomeComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(appRoutes),
    UnicaTextEditorComponent
  ],
})
export class RemoteEntryModule {
}
