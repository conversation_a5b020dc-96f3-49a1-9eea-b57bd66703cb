// Import Quill from centralized loader
import { EmbedBlot } from '../core/quill-loader';
import { Subject } from 'rxjs';

export interface TextNodeData {
  label: string;
  length: number;
  id?: string;
  messageBody?: string;
}

export interface TextNodeClickEvent {
  value: TextNodeData;
  action?: 'edit' | 'create';
  node?: HTMLElement;
}

export class EditorTextNode extends EmbedBlot {
  static blotName = 'editortextblock';
  static tagName = 'span';
  static className = 'droppable';

  static onTextNodeClick = new Subject<TextNodeClickEvent>();
  static TextNodeClick$ = EditorTextNode.onTextNodeClick.asObservable();

  static create(value: TextNodeData): HTMLElement {
    // Follow the exact pattern from Quill documentation
    const node = super.create() as HTMLElement;

    // Extract clean text content from the label (avoid nested HTML)
    let cleanText = value.label;

    // If the label contains HTML (from corrupted copy/paste), extract just the text
    if (cleanText.includes('<span') || cleanText.includes('&lt;span')) {
      // Create a temporary element to extract text content
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = cleanText;
      cleanText = tempDiv.textContent || tempDiv.innerText || cleanText;
    }

    // Set content as text only (not HTML) to prevent nested spans
    node.textContent = cleanText;

    // IMPORTANT: Add the droppable class for click detection
    node.classList.add('droppable');

    // Set attributes - only id and contenteditable
    node.setAttribute('contenteditable', 'false');
    if (value.id) {
      node.id = value.id;
    }

    // Store the value data as data attributes for event delegation
    // Store the clean text, not the potentially corrupted HTML
    node.setAttribute('data-text-node-id', value.id || '');
    node.setAttribute('data-text-node-label', cleanText);
    node.setAttribute('data-text-node-length', cleanText.length.toString());

    // Don't attach individual click listeners - use event delegation instead
    // The directive will handle clicks via event delegation on the editor root

    return node;
  }

  // Override value method to return the data stored in the node
  static value(node: HTMLElement): TextNodeData {
    // Use textContent instead of innerHTML to avoid nested HTML issues
    const text = node.textContent || '';
    return {
      label: text,
      length: text.length,
      id: node.id || undefined
    };
  }

  /**
   * Static method to trigger click events for event delegation
   * This handles clicks on both original and duplicated text nodes
   */
  static handleTextNodeClick(node: HTMLElement): void {
    // Extract data from the node (works for both original and duplicated nodes)
    let label = node.getAttribute('data-text-node-label') || '';
    let id = node.getAttribute('data-text-node-id') || node.id || undefined;

    // Check if label is corrupted (contains HTML entities from copy/paste)
    const isCorrupted = label.includes('&lt;span') || label.includes('&quot;');

    // If no label from attributes or corrupted, extract from DOM structure
    if (!label || isCorrupted) {
      label = EditorTextNode.extractCleanTextFromNode(node);
    }

    // If we don't have a proper ID from attributes, try to extract it from the label
    if (!id && label) {
      // If label is in format <--Display Text-->, try to convert to kebab-case ID
      const cleanLabel = label.replace(/^&lt;--(.+)--&gt;$/, '$1').replace(/^<--(.+)-->$/, '$1');
      if (cleanLabel !== label) {
        // Convert display text to potential ID (e.g., "User Name" -> "user-name")
        id = cleanLabel.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      }
    }

    const value: TextNodeData = {
      label: label,
      length: parseInt(node.getAttribute('data-text-node-length') || label.length.toString(), 10),
      id: id
    };

    // Emit click event
    EditorTextNode.onTextNodeClick.next({
      value,
      action: 'edit',
      node: node
    });
  }

  /**
   * Extract clean text from deeply nested span structure
   */
  static extractCleanTextFromNode(node: HTMLElement): string {
    // Try to find the deepest span with actual text content
    const allSpans = node.querySelectorAll('span[contenteditable="false"]');

    // Look for the span that contains the actual text (not HTML)
    for (let i = allSpans.length - 1; i >= 0; i--) {
      const span = allSpans[i] as HTMLElement;
      const text = span.innerHTML.trim();

      // Check if this span contains the actual text pattern
      if (text.match(/^[﻿]*[&<]?lt;--.*--[&>]?gt;[﻿]*$/)) {
        // Clean up the text
        return text
          .replace(/^[﻿]+|[﻿]+$/g, '') // Remove zero-width characters
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&amp;/g, '&');
      }
    }

    // Fallback: use the node's text content
    return node.textContent?.trim() || '';
  }
}

// Note: Blot is registered in the component to avoid conflicts
