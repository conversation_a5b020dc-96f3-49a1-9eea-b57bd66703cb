// import * as QuillNamespace from 'quill';
// (window as any).Quill = QuillNamespace.default ?? QuillNamespace;
// const Quill = (window as any).Quill;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
// const Inline = Quill.import('blots/inline') as any;
import { Quill, InlineBlot } from '../core/quill-loader';
class PersnalizationFields extends InlineBlot {
  static blotName = 'personalization';
  static tagName = 'span';
  static className = 'droppable';
  /**
   * this will actually create the ta
   */
  static create(value: { text?: string, id: string }) {
    const node = super.create();
    node.contentEditable = 'false';
    node.id = value.id;
    if (value.text) {
      node['innerHTML'] = value.text;
    }
    return node;
  }
  /**
   * This is mandatory to override else the code will not work
   * The blot/inline.js file has a code
   *    if (domNode.tagName === InlineBlot.tagName) return undefined;
   * Due to this is we do not override this emthod it will not work
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static formats(domNode: HTMLElement): any {
    if (typeof this.tagName === 'string') {
      return {
        id: domNode.id
      };
    }
    return undefined;
  }
  /**
   * get the data form the HTML tag
   */
  static value(node: HTMLElement) {
    return {
      text: node.innerHTML,
      id: node.getAttribute('id')
    }
  }
}
Quill.register(PersnalizationFields);
