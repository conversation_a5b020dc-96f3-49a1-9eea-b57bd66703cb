// Import Quill loader FIRST to ensure it's available before ngx-quill
import '@hcl/angular/unica-text-editor/core/quill-loader';

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AngularLibModule } from './app/angular-lib.module';


platformBrowserDynamic()
  .bootstrapModule(AngularLibModule, {
    ngZoneEventCoalescing: true,
  })
  .catch((err) => console.error(err));


//# sourceMappingURL=main.js.map
