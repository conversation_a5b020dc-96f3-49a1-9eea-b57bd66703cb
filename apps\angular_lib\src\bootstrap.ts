// Import Quill loader to ensure it's initialized early
import { ensureQuillReady } from '@hcl/angular/unica-text-editor';

// Ensure Quill is ready before anything else
ensureQuillReady();

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AngularLibModule } from './app/angular-lib.module';


platformBrowserDynamic()
  .bootstrapModule(AngularLibModule, {
    ngZoneEventCoalescing: true,
  })
  .catch((err) => console.error(err));


//# sourceMappingURL=main.js.map
