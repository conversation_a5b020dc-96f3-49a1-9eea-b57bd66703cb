// Import Quill polyfill FIRST to ensure Angular 19 ESM compatibility
import '@hcl/angular/unica-text-editor';

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AngularLibModule } from './app/angular-lib.module';


platformBrowserDynamic()
  .bootstrapModule(AngularLibModule, {
    ngZoneEventCoalescing: true,
  })
  .catch((err) => console.error(err));


//# sourceMappingURL=main.js.map
