// CRITICAL: Import and initialize Quill BEFORE any other imports
import * as QuillNamespace from 'quill';

// Handle Angular 19 ESM compatibility
const QuillConstructor = (QuillNamespace as any).default || QuillNamespace;

// Aggressively set Quill globally before ngx-quill loads
if (typeof window !== 'undefined') {
  (window as any).Quill = QuillConstructor;
  (globalThis as any).Quill = QuillConstructor;

  console.log('🚀 Quill force-initialized before ngx-quill');
}

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AngularLibModule } from './app/angular-lib.module';


platformBrowserDynamic()
  .bootstrapModule(AngularLibModule, {
    ngZoneEventCoalescing: true,
  })
  .catch((err) => console.error(err));


//# sourceMappingURL=main.js.map
