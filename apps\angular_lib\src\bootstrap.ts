// Import Quill loader to ensure it's initialized early
import { ensureQuillReady } from '@hcl/angular/unica-text-editor';

// Ensure Quill is ready before anything else
console.log('🚀 Bootstrap: Ensuring Quill is ready...');
const quillConstructor = ensureQuillReady();
console.log('🚀 Bootstrap: Quill constructor:', typeof quillConstructor, quillConstructor);
console.log('🚀 Bootstrap: window.Quill:', typeof (window as any).Quill, (window as any).Quill);

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AngularLibModule } from './app/angular-lib.module';


platformBrowserDynamic()
  .bootstrapModule(AngularLibModule, {
    ngZoneEventCoalescing: true,
  })
  .catch((err) => console.error(err));


//# sourceMappingURL=main.js.map
