import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community'; // AG Grid Community Module
import { AgGridAngular } from 'ag-grid-angular';
import type { ColDef, GridApi, GridReadyEvent } from "ag-grid-community";

// Register all Community features
ModuleRegistry.registerModules([AllCommunityModule]);


@Component({
  selector: 'unica-datagrid',
  standalone: true,
  imports: [CommonModule, AgGridAngular],
  templateUrl: './unica-datagrid.component.html',
  styleUrl: './unica-datagrid.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})


export class UnicaDatagridComponent {
  @Output() gridLoaded = new EventEmitter<GridReadyEvent>();
  @Input() rowData: unknown[] = [];
  @Input() columnDefs: ColDef[] = [];
  @Input() defaultColDef: ColDef = {
    flex: 1,
  };

  // Grid API: Provides access to the grid's API methods.
  gridApi!: GridApi;
  
  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    this.gridLoaded.emit(event);
  }

}
