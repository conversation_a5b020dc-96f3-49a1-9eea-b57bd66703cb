/**
 * Standalone Quill Polyfill for Angular 19 ESM Compatibility
 * This file can be imported in any application's main.ts or bootstrap.ts
 * to ensure Quill is properly loaded before ngx-quill components
 */

// Import Quill with Angular 19 ESM compatibility
import * as QuillNamespace from 'quill';

// Handle Angular 19's strict ESM - get the actual constructor
const QuillConstructor = (QuillNamespace as any).default || QuillNamespace;

// Verify we have the constructor
if (typeof QuillConstructor !== 'function') {
  console.error('❌ Failed to get Quill constructor from import. Got:', typeof QuillConstructor, QuillConstructor);
  throw new Error('Quill is not a constructor after Angular 19 ESM import');
}

// Ensure Quill is available globally for ngx-quill
if (typeof window !== 'undefined') {
  (window as any).Quill = QuillConstructor;
  
  // Also set on globalThis for better compatibility
  if (typeof globalThis !== 'undefined') {
    (globalThis as any).Quill = QuillConstructor;
  }
  
  console.log('✅ Quill constructor loaded and exposed globally for Angular 19 ESM compatibility');
  
  // Verify the global assignment worked
  if (typeof (window as any).Quill !== 'function') {
    console.error('❌ Failed to set window.Quill as constructor');
  } else {
    console.log('✅ Verified: window.Quill is available as constructor');
  }
}

// Export the Quill constructor for direct use
export const Quill = QuillConstructor;

// Export type alias for TypeScript
export type QuillInstance = InstanceType<typeof QuillConstructor>;

// Utility function to ensure Quill is ready
export function ensureQuillReady(): typeof QuillConstructor {
  if (typeof window !== 'undefined' && !(window as any).Quill) {
    (window as any).Quill = QuillConstructor;
  }
  return QuillConstructor;
}

// Default export for convenience
export default QuillConstructor;
