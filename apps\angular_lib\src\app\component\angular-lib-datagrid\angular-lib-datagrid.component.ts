import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UnicaDatagridComponent } from '@hcl/angular/unica-datagrid';
import { ColDef, GridApi, GridReadyEvent } from 'ag-grid-community';

@Component({
  selector: 'angular-lib-datagrid',
  standalone: true,
  imports: [CommonModule, UnicaDatagridComponent],
  templateUrl: './angular-lib-datagrid.component.html',
  styleUrl: './angular-lib-datagrid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AngularLibDatagridComponent implements OnInit {

  gridApi!: GridApi;
  rowData: unknown[] = [];
  colDefs: ColDef[] = [];
  defaultColDef: ColDef = {
    width: 150,
    cellStyle: { fontWeight: 'bold' },
    flex: 1,
  };

  onGridReady(param: GridReadyEvent) {
    this.gridApi = param.api;
  }

  ngOnInit() {
    this.rowData = [
      { make: "Tesla", model: "Model Y", price: 64950, electric: true },
      { make: "Ford", model: "F-Series", price: 33850, electric: false },
      { make: "Toyota", model: "Corolla", price: 29600, electric: false },
    ];

    // Column Definitions: Defines the columns to be displayed.
    this.colDefs = [
      { field: "make" },
      { field: "model" },
      { field: "price" },
      { field: "electric" }
    ];

    this.defaultColDef = {
      width: 150,
      cellStyle: { fontWeight: 'bold' },
      flex: 1,
    };
  }
}
