// Import Quill from centralized loader
import { Quill, InlineBlot } from '../core/quill-loader';
/**
 * The class for the personalization fields
 */
class CutomLink extends InlineBlot {
  static blotName = 'customLink';
  static tagName = 'a';
  static className = 'custom-link';
  // static className: string = 'custom-link';
  /**
   * this will actually create the ta
   */
  static create(data: {
    type?: string;
    link?: string;
    name?: string;
    id?: string;
    rulelength?: string;
    newWindow?: boolean;
    aliasName?: string;
  }) {
    const node = super.create();
    // node.classList.add('custom-link');
    node.classList.add('droppable');
    node.classList.add('link-droppable');
    if (data.newWindow) {
      node.setAttribute('target', '_blank');
    }
    if (data.type) {
      node.setAttribute('data-type', data.type);
    }
    if (data.name) {
      node.setAttribute('href', data.name);
    } else {
      node.setAttribute('href', data.link);
    }
    if (data.link) {
      node.setAttribute('data-href', data.link);
    }
    if (data.name) {
      node.setAttribute('data-name', data.name);
    }
    if (data.id) {
      node.setAttribute('data-id', data.id);
    }
    if (data.rulelength) {
      node.setAttribute('data-rulelength', data.rulelength);
    }
    if (data.aliasName) {
      node.setAttribute('data-alias', data.aliasName);
    }
    return node;
  }
  /**
   * This is mandatory to override else the code will not work
   * The blot/inline.js file has a code
   *    if (domNode.tagName === InlineBlot.tagName) return undefined;
   * Due to this is we do not override this emthod it will not work
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static formats(domNode: HTMLElement): any {
    if (typeof this.tagName === 'string') {
      return {
        text: domNode.innerHTML,
        link: domNode.getAttribute('data-href') || domNode.getAttribute('href'),
        type: domNode.getAttribute('data-type'),
        name: domNode.getAttribute('data-name'),
        id: domNode.getAttribute('data-id'),
        newWindow: domNode.getAttribute('target') ? true : false,
        rulelength: domNode.getAttribute('data-rulelength'),
        aliasName: domNode.getAttribute('data-alias'),
      };
    }
    return undefined;
  }
  /**
   * get the data form the HTML tag
   */
  static value(node: HTMLElement) {
    return {
      text: node.innerHTML,
      link: node.getAttribute('data-href') || node.getAttribute('href'),
      type: node.getAttribute('data-type'),
      name: node.getAttribute('data-name'),
      id: node.getAttribute('data-id'),
      newWindow: node.getAttribute('target') ? true : false,
      rulelength: node.getAttribute('data-rulelength'),
      aliasName: node.getAttribute('data-alias'),
    };
  }
}
Quill.register(CutomLink);
