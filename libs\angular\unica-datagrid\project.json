{"name": "unica-datagrid", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/angular/unica-datagrid/src", "prefix": "unica", "projectType": "library", "tags": ["lib:angular-datagrid", "lib:angular"], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/angular/unica-datagrid/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/angular/unica-datagrid/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/angular/unica-datagrid/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/angular/unica-datagrid/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}