import { APP_INITIALIZER, ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { appRoutes } from './app.routes';
import {
  HTTP_INTERCEPTORS,
  HttpClient,
  provideHttpClient,
  withInterceptorsFromDi
} from '@angular/common/http';
import { ApplicationService, RequestTokenInterceptor, UnicaTranslateLoader } from '@hcl/angular/unica-angular-common';
import { CdpApplicationService } from './service/cdp-application.service';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { firstValueFrom } from 'rxjs';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { provideQuillConfig } from 'ngx-quill/config';

/**
 * We have to tell the translation modules on how we will load the localized resources
 * This function tells the module that we will l oad it from /assets/i18n/*.json file
 */
function HttpLoaderFactory(http: HttpClient) {
  return new UnicaTranslateLoader(http, [
    {prefix : `/assets/i18n/common/`, sufix: '.json'},
    {prefix : `/assets/i18n/cdp/`, sufix: '.json'}
  ]);
}
/**
 * This is the initialization method that is called bcoz we have it in provider array with APP_INITIALIZER
 * all initialization can be done here, the main element we require b4 every thing loads is the localization
 * so we have to set the locale here
 */
function appInitializerFactory(translate: TranslateService) {
  return () => {
    const translationObject =  translate.use('en_US');
    return firstValueFrom(translationObject);
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideHttpClient(
      withInterceptorsFromDi()
    ),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(appRoutes),
    // Provide Quill configuration for standalone components
    provideQuillConfig({
      modules: {
        toolbar: [
          ['bold', 'italic', 'underline'],
          [{ 'color': [] }, 'link']
        ]
      }
    }),
    importProvidersFrom(
      BrowserAnimationsModule,
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
        },
        isolate: false
      }),
    ),
    {
      provide: ApplicationService,
      useClass: CdpApplicationService
    },
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true
    },
    {
      provide: APP_INITIALIZER,
      useFactory: (applicationService: ApplicationService) => {
        return () => {
          return applicationService.init();
        };
      },
      deps: [ApplicationService],
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: RequestTokenInterceptor,
      multi: true
    },
  ],
};
