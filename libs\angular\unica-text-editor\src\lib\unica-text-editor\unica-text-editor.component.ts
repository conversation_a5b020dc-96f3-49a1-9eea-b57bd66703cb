import TextAlignQuillModule from '../extentions/text-align.config';
import { TextAutocompleteDirective } from '../directives';
import { QuillTagsService, TagSuggestion } from '../services';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  input,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
  DestroyRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
// Import Quill from centralized loader
import { Quill, QuillInstance } from '../core/quill-loader';
import {
  QuillEditorComponent,
  QuillModule,
  QuillToolbarConfig,
} from 'ngx-quill';
import { EditorTextNode } from '../extentions/text-node';
// Register the custom blots for Quill
try {
  Quill.register('formats/editortextblock', EditorTextNode, true);
} catch (error) {
  console.warn('Blot registration warning:', error);
}
import { FormsModule } from '@angular/forms';
import { computeEditorStyles } from '../utils/style.utils';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TextEditorStyles } from '../editor.model';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

/**
 * Generate a unique ID for internal use
 * @param length - Length of the ID to generate
 */
function generateUniqueId(length = 10): string {
  let id = '';
  const selectedChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz-**********';
  for (let i = 0; i < length; i++) {
    id += selectedChars.charAt(Math.floor(Math.random() * selectedChars.length));
  }
  return id;
}

@Component({
  selector: 'unica-text-editor',
  standalone: true,
  imports: [
    CommonModule,
    QuillModule,
    FormsModule,
    TranslateModule,
    TextAutocompleteDirective,
  ],
  templateUrl: './unica-text-editor.component.html',
  styleUrls: [
    './unica-text-editor.component.scss',
    '../styles/hashtag-autocomplete.styles.scss',
  ],
  providers: [QuillTagsService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None, // Required for Quill styles to work properly
  host: {
    class: 'unica-text-editor-host',
  },
})
export class UnicaTextEditorComponent implements OnInit {
  translate = inject(TranslateService);
  private tagsService = inject(QuillTagsService);
  private destroyRef = inject(DestroyRef)
  /**
   * Reference to the Quill editor component
   */ @ViewChild(QuillEditorComponent, { static: false })
  quillEditorComponent!: QuillEditorComponent;

  private _html = '';
  private _lastRawContent = '';

  @Input()
  set content(value: string | null | undefined) {
    const raw = value ?? '';
    if (raw !== this._lastRawContent) {
      this._lastRawContent = raw;
      this._html = raw ? this.parseHTMLForHyperlinks(raw) : '';
    }
  }

  get content(): string {
    return this._html;
  }

  /**
   * Event emitter for content changes
   */
  @Output() contentChange = new EventEmitter<string>();

  /**
   * Whether the editor is read-only
   */
  readOnly = input(false);

  /**
   * Placeholder for the editor
   */
  placeholder = input(this.translate.instant('TEXT_EDITOR.PLACEHOLDER'));

  /**
   * Styles for the text editor
   */
  styles = input<TextEditorStyles | undefined>(undefined);

  required = input(true);

  /**
   * Hashtag suggestions for the autocomplete dropdown
   */
  @Input()
  set suggestionList(suggestions: { id: string; display: string }[]) {
    this._suggestionList = suggestions || [];
    // Update Tags service when suggestions change
    if (this.tagsService) {
      this.tagsService.updateSuggestions(this._suggestionList);
    }
  }

  get suggestionList(): { id: string; display: string }[] {
    return this._suggestionList;
  }

  private _suggestionList: { id: string; display: string }[] = [];

  /**
   * Event emitted when a hashtag suggestion is selected
   */
  @Output() hashtagSelected = new EventEmitter<{
    id: string;
    display: string;
  }>();
  public editorInstance!: QuillInstance;
  private contentChangeSubject = new Subject<string>();
  ngOnInit() {
    TextAlignQuillModule();
    this.initializeTagsService();
    this.contentChangeSubject
      .pipe(debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe((event) => {
        this.contentChange.emit(event);
      });
  }

  /**
   * Initialize Tags service with current suggestions
   */
  private initializeTagsService(): void {
    this.tagsService.initialize({
      suggestions: this.suggestionList,
      onTagSelected: (tag: TagSuggestion) => {
        this.hashtagSelected.emit(tag);
      },
      insertFormat: 'textblock',
      addSpace: true,
    });
  }

  /**
   * Get the available formats for the Quill editor
   */
  getQuillFormats(): string[] {
    return [
      'bold',
      'italic',
      'underline',
      'strike',
      'blockquote',
      'code-block',
      'header',
      'list',
      'script',
      'indent',
      'direction',
      'size',
      'color',
      'background',
      'font',
      'align',
      'link',
      'editortextblock',
    ];
  }

  private parseHTMLForHyperlinks(b: string) {
    const domObject = new DOMParser().parseFromString(b, 'text/html');
    const allLinks = domObject.querySelectorAll('a');
    allLinks.forEach((elem) => {
      if (!elem.getAttribute('data-id')) {
        elem.setAttribute('data-id', generateUniqueId());
      }
    });
    b = domObject.body.innerHTML;

    return b;
  }

  getQuillConfig() {
    const container: QuillToolbarConfig = [
      ['bold', 'italic', 'underline'],
      [
        {
          color: [
            '#000000',
            '#e60000',
            '#ff9900',
            '#ffff00',
            '#008a00',
            '#0066cc',
            '#9933ff',
            '#ffffff',
            '#facccc',
            '#ffebcc',
            '#ffffcc',
            '#cce8cc',
            '#cce0f5',
            '#ebd6ff',
            '#bbbbbb',
            '#f06666',
            '#ffc266',
            '#ffff66',
            '#66b966',
            '#66a3e0',
            '#c285ff',
            '#888888',
            '#a10000',
            '#b26b00',
            '#b2b200',
            '#006100',
            '#0047b2',
            '#6b24b2',
            '#444444',
            '#5c0000',
            '#663d00',
            '#666600',
            '#003700',
            '#002966',
            '#3d1466',
            'custom-color',
          ],
        },
        'link',
      ],
    ];

    if (this.suggestionList.length > 0) {
      this.tagsService.addTagsToToolbar(container);
    }

    return {
      toolbar: {
        container,
        handlers: {
          ...this.tagsService.getToolbarHandlers(),
        },
      },
    };
  }

  /**
   * Handler for placeholder button in toolbar
   */
  placeholderHandler(): void {
    // Placeholder handler implementation
  }

  /**
   * Handler for hyperlink button in toolbar
   */
  hyperLinkHandler(): void {
    // Hyperlink handler implementation
  }

  /**
   * Handler for rule button in toolbar
   */
  ruleHandler(): void {
    // Rule handler implementation
  }

  /**
   * Handler for AI button in toolbar
   */
  aiHandler(): void {
    // AI handler implementation
  }

  /**
   * Handler for content changes
   * @param event - The content change event from the editor
   */
  onContentChanged(event: string): void {
    this.contentChangeSubject.next(event);
  }

  /**
   * Handler for editor creation
   * @param editor - The Quill editor instance
   */
  onEditorCreated(editor: QuillInstance): void {
    this.editorInstance = editor;
    if (!this.content || this.content === '') {
      editor.setText('');
    }
    this.tagsService.setEditor(editor);
  }

  /**
   * Get computed styles for the editor based on the styles input
   * Uses utility function for better maintainability
   */
  getComputedStyles(): Record<string, string> {
    return computeEditorStyles(this.styles());
  }

  /**
   * Handle a selected hashtag from the autocomplete directive
   * @param suggestion - The selected hashtag suggestion
   */
  onHashtagSelected(suggestion: { id: string; display: string }): void {
    this.hashtagSelected.emit(suggestion);
  }
}
