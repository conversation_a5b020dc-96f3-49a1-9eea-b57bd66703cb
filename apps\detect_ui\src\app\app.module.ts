
import { APP_INITIALIZER, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, RouterOutlet } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AppComponent } from './app.component';
import { UnicaAppBarComponent } from '@hcl/angular/unica-app-bar';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UnicaSearchComponent } from '@hcl/angular/unica-input';
import { RemoteEntryModule } from './remote-entry/entry.module';
import { HttpClient, provideHttpClient } from '@angular/common/http';
import { UnicaTranslateLoader } from '@hcl/angular/unica-angular-common';
import { TranslateService, TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { firstValueFrom } from 'rxjs';
import { DetectAppRoutes } from './detect.routes';
import { UnicaSideNavContainerComponent } from '@hcl/angular/unica-side-nav';


/**
 * We have to tell the translation modules on how we will load the localized resources
 * This function tells the module that we will l oad it from /assets/i18n/*.json file
 */
function HttpLoaderFactory(http: HttpClient) {
  return new UnicaTranslateLoader(http, [
    { prefix: `/assets/i18n/`, sufix: '.json' }
  ]);
}

/**
 * This is the initialization method that is called bcoz we have it in provider array with APP_INITIALIZER
 * all initialization can be done here, the main element we require b4 every thing loads is the localization
 * so we have to set the locale here
 */
function appInitializerFactory(translate: TranslateService) {
  return () => {
    const translationObject = translate.use('it_IT');
    return firstValueFrom(translationObject);
  };
}

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    CommonModule,
    BrowserModule,
    BrowserAnimationsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      isolate: false,
    }),
    RouterOutlet,
    RouterModule.forRoot(DetectAppRoutes),
    RemoteEntryModule,
    UnicaTypographyComponent,
    UnicaAppBarComponent,
    UnicaSearchComponent,
    UnicaSideNavContainerComponent
  ],
  providers: [
    provideHttpClient(),
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
  exports: [],
})
export class AppModule { }
