import { QuillToolbarConfig } from 'ngx-quill';
import { QuillTagsService, TagSuggestion, TagsConfig } from '../services/quill-tags.service';
// Import Quill from centralized loader
import { QuillInstance } from '../core/quill-loader';

/**
 * Helper utility for easy integration of Tags functionality into any Quill editor
 * This provides a simple plug-and-play interface
 */
export class QuillTagsHelper {
  private tagsService: QuillTagsService;

  constructor(tagsService: QuillTagsService) {
    this.tagsService = tagsService;
  }

  /**
   * Easy setup method - call this to add Tags to your Quill editor
   * @param config - Tags configuration
   * @returns Object with toolbar config and handlers
   */
  setup(config: TagsConfig): {
    addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
    getHandlers: () => { [key: string]: (value: string) => void };
    onEditorCreated: (editor: QuillInstance) => void;
  } {
    // Initialize the service
    this.tagsService.initialize(config);

    return {
      addToToolbar: (container: QuillToolbarConfig) => {
        return this.tagsService.addTagsToToolbar(container);
      },
      getHandlers: () => {
        return this.tagsService.getToolbarHandlers();
      },
      onEditorCreated: (editor: QuillInstance) => {
        this.tagsService.setEditor(editor);
      }
    };
  }
}

/**
 * Factory function to create a QuillTagsHelper instance
 */
export function createQuillTagsHelper(tagsService: QuillTagsService): QuillTagsHelper {
  return new QuillTagsHelper(tagsService);
}

/**
 * Standalone function for quick integration without dependency injection
 * Use this when you want to quickly add Tags to a Quill editor
 */
export function addTagsToQuill(config: {
  suggestions: TagSuggestion[];
  onTagSelected?: (tag: TagSuggestion) => void;
  insertFormat?: 'textblock' | 'text';
  addSpace?: boolean;
}): {
  addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
  getHandlers: () => { [key: string]: (value: string) => void };
  onEditorCreated: (editor: QuillInstance) => void;
} {
  const tagsService = new QuillTagsService();
  const helper = new QuillTagsHelper(tagsService);

  return helper.setup({
    suggestions: config.suggestions,
    onTagSelected: config.onTagSelected,
    insertFormat: config.insertFormat || 'textblock',
    addSpace: config.addSpace !== false
  });
}

/**
 * Quick setup function with default suggestions
 */
export function addDefaultTagsToQuill(onTagSelected?: (tag: TagSuggestion) => void): {
  addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
  getHandlers: () => { [key: string]: (value: string) => void };
  onEditorCreated: (editor: QuillInstance) => void;
} {
  return addTagsToQuill({
    suggestions: getDefaultSuggestions(),
    onTagSelected
  });
}

/**
 * Get default tag suggestions for common use cases
 */
export function getDefaultSuggestions(): TagSuggestion[] {
  return [
    // User Information Tags
    { id: 'user-name', display: 'User Name' },
    { id: 'user-email', display: 'User Email' },
    { id: 'user-phone', display: 'User Phone' },
    { id: 'user-company', display: 'User Company' },
    { id: 'user-title', display: 'User Title' },

    // Date & Time Tags
    { id: 'current-date', display: 'Current Date' },
    { id: 'current-time', display: 'Current Time' },
    { id: 'due-date', display: 'Due Date' },
    { id: 'created-date', display: 'Created Date' },

    // Document Tags
    { id: 'document-title', display: 'Document Title' },
    { id: 'document-id', display: 'Document ID' },
    { id: 'document-version', display: 'Document Version' },

    // System Tags
    { id: 'system-name', display: 'System Name' },
    { id: 'environment', display: 'Environment' },
    { id: 'application-url', display: 'Application URL' },

    // Custom Tags
    { id: 'project-name', display: 'Project Name' },
    { id: 'department', display: 'Department' },
    { id: 'location', display: 'Location' },
    { id: 'reference-number', display: 'Reference Number' },
    { id: 'status', display: 'Status' },
  ];
}




