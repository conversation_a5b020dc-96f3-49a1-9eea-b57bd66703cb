import { APP_INITIALIZER, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule, RouterOutlet } from '@angular/router';
import { AngularLibComponent } from './angular-lib.component';
import { AngularLibAppBarComponent } from './component/angular-lib-app-bar/angular-lib-app-bar.component';
import { UnicaAppBarComponent } from '@hcl/angular/unica-app-bar';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import {
  UnicaInputComponent,
  UnicaSearchComponent,
} from '@hcl/angular/unica-input';
import { RemoteEntryModule } from './remote-entry/remote-entry.module';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient, provideHttpClient } from '@angular/common/http';
import { UnicaTranslateLoader } from '@hcl/angular/unica-angular-common';
import { firstValueFrom } from 'rxjs';
import { UnicaSideNavContainerComponent } from '@hcl/angular/unica-side-nav';
import { QuillModule } from 'ngx-quill';

/**
 * We have to tell the translation modules on how we will load the localized resources
 * This function tells the module that we will l oad it from /assets/i18n/*.json file
 */
function HttpLoaderFactory(http: HttpClient) {
  return new UnicaTranslateLoader(http, [
    { prefix: `/assets/i18n/common/`, sufix: '.json' },
    { prefix: `/assets/i18n/angular_lib/`, sufix: '.json' }
  ]);
}

/**
 * This is the initialization method that is called bcoz we have it in provider array with APP_INITIALIZER
 * all initialization can be done here, the main element we require b4 every thing loads is the localization
 * so we have to set the locale here
 */
function appInitializerFactory(translate: TranslateService) {
  return () => {
    const translationObject = translate.use('en_US');
    return firstValueFrom(translationObject);
  };
}

@NgModule({
  declarations: [
    AngularLibComponent
  ],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      },
      isolate: false
    }),
    QuillModule.forRoot(),
    BrowserModule,
    RouterOutlet,
    RouterModule.forRoot([]),
    RemoteEntryModule,
    UnicaAppBarComponent,
    UnicaTypographyComponent,
    UnicaInputComponent,
    UnicaSearchComponent,
    AngularLibAppBarComponent,
    UnicaSideNavContainerComponent
  ],
  providers: [
    provideHttpClient(),
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
  ],
  bootstrap: [AngularLibComponent],
})
export class AngularLibModule {}
