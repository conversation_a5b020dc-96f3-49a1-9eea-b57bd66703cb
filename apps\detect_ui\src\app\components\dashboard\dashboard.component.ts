import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../../services/common-service';

@Component({
  selector: 'hcldetect-dashboard',
  templateUrl: './dashboard.component.html',
  styles: ``,
  standalone: false
})

export default class DashboardComponent {
  title = 'new';

  constructor(public translate: TranslateService, public commonService: CommonService) {

    setTimeout(() => {
      this.title = 'old';
    }, 5000);
  }

  public changeDetectCount() {
    this.commonService.detectCount.set({ name: '<PERSON>', title: '<PERSON><PERSON>' });
  }
}
