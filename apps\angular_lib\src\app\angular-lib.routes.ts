import { Route } from '@angular/router';
import { AngularLibSideNavDocComponent } from './component/angular-lib-side-nav-doc/angular-lib-side-nav-doc.component';
import { AngularLibHomeComponent } from './component/angular-lib-home/angular-lib-home.component';
import { AngularLibAppBarDocComponent } from './component/angular-lib-app-bar-doc/angular-lib-app-bar-doc.component';
import {
  AngularLibDashboardBuilderComponent
} from './component/angular-lib-dashboard-builder-doc/angular-lib-dashboard-builder.component';
import { AngularLibSelectBoxComponent } from './component/angular-lib-select-box/angular-lib-select-box.component';
import { AngularLibTextAreaComponent } from './component/angular-lib-text-area/angular-lib-text-area.component';
import {
  AngularLibEmailEditorComponent
} from './component/angular-lib-email-editor/angular-lib-email-editor.component';
import { AngularLibDatagridComponent } from './component/angular-lib-datagrid/angular-lib-datagrid.component';

/**
 * The routes used by the application
 */
export const appRoutes: Route[] = [
  {
    path: 'home',
    component: AngularLibHomeComponent,
    data: {
      label: 'Home',
      icon: 'home',
      id: 'home'
    }
  },
  {
    path: 'datagrid',
    component: AngularLibDatagridComponent,
    data: {
      label: 'Unica Datagrid',
      icon: 'grid_on',
      id: 'data_grid'
    }
  },
  // add Input fields mappings
  {
    path: 'input',
    data: {
      label: 'Input Elements',
      icon: 'edit_note',
      id: 'input'
    },
    children: [
      {
        path: 'select',
        component: AngularLibSelectBoxComponent,
        data: {
          label: 'Select Box',
          icon: 'text_fields',
          id: 'select'
        }
      },
      {
              path: 'text-area',
              component: AngularLibTextAreaComponent,
              data: {
                label: 'Text Area',
                icon: 'notes',
                id: 'text-area'
              }
            }
    ]
  },
  // add Navigation mappings
  {
    path: 'nav',
    data: {
      label: 'Navigation Elements',
      icon: 'navigation',
      id: 'nav'
    },
    children: [
      {
        path: 'side-nav',
        component: AngularLibSideNavDocComponent,
        data: {
          label: 'Side navigation',
          icon: 'dock_to_right',
          id: 'sideNav'
        }
      },
      {
        path: 'app-bar',
        component: AngularLibAppBarDocComponent,
        data: {
          label: 'App Bar',
          icon: 'home',
          id: 'appBar'
        }
      }
    ]
  },
  {
    path: 'dashboard',
    component: AngularLibDashboardBuilderComponent,
    data: {
      label: 'Dashboard',
      icon: 'dashboard',
      id: 'dashboard'
    }
  },
  {
    path: 'email',
    component: AngularLibEmailEditorComponent,
    data: {
      label: 'Email Builder',
      icon: 'email',
      id: 'email'
    }
  },
  { path: '', redirectTo: 'home', pathMatch:'full' }
]
