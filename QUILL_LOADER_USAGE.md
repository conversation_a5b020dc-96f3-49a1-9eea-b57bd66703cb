# Centralized Quill Loader Usage Guide

## Overview

The centralized Quill loader (`libs/angular/unica-text-editor/src/lib/core/quill-loader.ts`) solves the Angular 19 ESM compatibility issue by providing a single point of Quill import and global exposure.

## How It Works

### 1. **Centralized Import Handling**
```typescript
// quill-loader.ts
import * as QuillNamespace from 'quill';

// Handle Angular 19's strict ESM - get the actual constructor
const QuillConstructor = (QuillNamespace as any).default || QuillNamespace;

// Ensure Quill is available globally for ngx-quill
if (typeof window !== 'undefined') {
  (window as any).Quill = QuillConstructor;
}

export const Quill = QuillConstructor;
export type QuillInstance = InstanceType<typeof QuillConstructor>;
```

### 2. **Global Exposure for ngx-quill**
The loader automatically ensures `window.Quill` is available as a constructor function, which is exactly what ngx-quill expects.

### 3. **Consistent Imports Everywhere**
All components, services, and extensions now import from the centralized loader:

```typescript
// Before (duplicated ESM handling)
import * as QuillNamespace from 'quill';
const Quill = QuillNamespace.default || QuillNamespace;
// ... global assignment code

// After (centralized)
import { Quill, QuillInstance } from '../core/quill-loader';
```

## Usage in Applications

### Option 1: Import the Loader Early (Recommended)

In your application's main component or bootstrap file:

```typescript
// apps/your-app/src/main.ts or app.component.ts
import { ensureQuillReady } from '@hcl/angular/unica-text-editor';

// Ensure Quill is ready before ngx-quill components load
ensureQuillReady();

// Then bootstrap your app or load components
```

### Option 2: Import in Components Using Text Editor

```typescript
// your-component.ts
import { Component } from '@angular/core';
import { Quill, QuillInstance } from '@hcl/angular/unica-text-editor';

@Component({
  template: `
    <unica-text-editor 
      [content]="content"
      (onEditorCreated)="onEditorCreated($event)">
    </unica-text-editor>
  `
})
export class YourComponent {
  content = '';

  onEditorCreated(editor: QuillInstance): void {
    // editor is properly typed and ready to use
    console.log('Editor ready:', editor);
  }
}
```

### Option 3: Use in Custom Quill Extensions

```typescript
// custom-extension.ts
import { Quill, EmbedBlot, InlineBlot, Parchment } from '@hcl/angular/unica-text-editor';

class CustomBlot extends EmbedBlot {
  static blotName = 'custom';
  static tagName = 'span';
  
  static create(value: any) {
    const node = super.create();
    // ... your custom logic
    return node;
  }
}

// Register the blot
Quill.register('formats/custom', CustomBlot);
```

## Benefits

### ✅ **Single Source of Truth**
- One place to handle Angular 19 ESM compatibility
- Consistent Quill import across all files
- Centralized global exposure logic

### ✅ **Type Safety**
- Proper TypeScript types exported
- `QuillInstance` type for method parameters
- IntelliSense support throughout

### ✅ **Maintainability**
- No duplicated ESM handling code
- Easy to update if Quill changes export structure
- Clear dependency management

### ✅ **ngx-quill Compatibility**
- Automatically ensures `window.Quill` is available
- Works with all ngx-quill components
- No additional configuration needed

## Exported Items

```typescript
// Main exports
export const Quill: QuillConstructor;
export type QuillInstance = InstanceType<typeof QuillConstructor>;

// Common Quill imports
export const Parchment;
export const EmbedBlot;
export const InlineBlot;
export const BlockBlot;

// Utility function
export function ensureQuillReady(): typeof QuillConstructor;

// Default export
export default Quill;
```

## Migration from Old Pattern

### Before (Multiple Files)
```typescript
// File 1
import * as QuillNamespace from 'quill';
const Quill = QuillNamespace.default || QuillNamespace;
(window as any).Quill = Quill;

// File 2
import * as QuillNamespace from 'quill';
const Quill = QuillNamespace.default || QuillNamespace;
(window as any).Quill = Quill;

// ... repeated in every file
```

### After (Centralized)
```typescript
// All files
import { Quill, QuillInstance } from '@hcl/angular/unica-text-editor';
```

This approach eliminates code duplication and provides a robust solution for Angular 19 ESM compatibility with Quill and ngx-quill.
