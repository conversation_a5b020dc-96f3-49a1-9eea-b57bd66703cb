import { Component } from '@angular/core';
import { ApplicationService } from '@hcl/angular/unica-angular-common';

@Component({
  selector: 'angular-lib-home',
  templateUrl: './angular-lib-home.component.html',
  styleUrl: './angular-lib-home.component.scss',
  standalone: false
})
export class AngularLibHomeComponent {
  content = '<p>Hello from the text editor!</p>';

  suggestions = [
    { id: 'user1', display: '<PERSON>' },
    { id: 'user2', display: '<PERSON>' },
    { id: 'tag1', display: 'Important' },
    { id: 'tag2', display: 'Urgent' }
  ];

  constructor(private appS: ApplicationService) {
  }

  onContentChange(content: string): void {
    console.log('Content changed:', content);
    this.content = content;
  }

  onHashtagSelected(tag: { id: string; display: string }): void {
    console.log('Hashtag selected:', tag);
  }
}
