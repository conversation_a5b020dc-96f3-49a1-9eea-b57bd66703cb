import { Component, OnInit } from '@angular/core';
import { ApplicationService } from '@hcl/angular/unica-angular-common';

@Component({
  selector: 'angular-lib-home',
  templateUrl: './angular-lib-home.component.html',
  styleUrl: './angular-lib-home.component.scss',
  standalone: false
})
export class AngularLibHomeComponent implements OnInit {
  content = '<p>Hello from the text editor!</p>';

  suggestions = [
    { id: 'user1', display: '<PERSON>' },
    { id: 'user2', display: '<PERSON>' },
    { id: 'tag1', display: 'Important' },
    { id: 'tag2', display: 'Urgent' }
  ];

  constructor(private appS: ApplicationService) {
  }

  ngOnInit(): void {
    // Debug: Check if Quill is available globally
    console.log('🔍 Debugging Quill availability:');
    console.log('  window.Quill:', typeof (window as any).Quill);
    console.log('  window.Quill constructor:', (window as any).Quill);

    if (typeof (window as any).Quill === 'function') {
      console.log('✅ window.Quill is available as constructor');
      try {
        const testInstance = new (window as any).Quill(document.createElement('div'));
        console.log('✅ Successfully created test Quill instance:', testInstance);
      } catch (error) {
        console.error('❌ Failed to create test Quill instance:', error);
      }
    } else {
      console.error('❌ window.Quill is not available or not a constructor');
    }
  }

  onContentChange(content: string): void {
    console.log('Content changed:', content);
    this.content = content;
  }

  onHashtagSelected(tag: { id: string; display: string }): void {
    console.log('Hashtag selected:', tag);
  }
}
