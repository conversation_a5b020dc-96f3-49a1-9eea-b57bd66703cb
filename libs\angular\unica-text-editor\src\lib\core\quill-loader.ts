/**
 * Centralized Quill Loader for Angular 19 ESM Compatibility
 * This module handles the proper loading and exposure of Quill for ngx-quill
 */

// Import Quill with Angular 19 ESM compatibility
import * as QuillNamespace from 'quill';

// Handle Angular 19's strict ESM - get the actual constructor
const QuillConstructor = (QuillNamespace as any).default || QuillNamespace;

// Verify we have the constructor
if (typeof QuillConstructor !== 'function') {
  console.error('❌ Quill import failed. Expected constructor, got:', typeof QuillConstructor);
  throw new Error('Quill is not a constructor - Angular 19 ESM compatibility issue');
}

// Comprehensive Quill initialization for ngx-quill compatibility
if (typeof window !== 'undefined') {
  // Use a more defensive approach to avoid webpack optimization issues
  const globalWindow = window as any;
  const globalThis_ = globalThis as any;

  // Set on window (primary method for ngx-quill)
  globalWindow['Quill'] = QuillConstructor;

  // Set on globalThis for broader compatibility
  if (typeof globalThis !== 'undefined') {
    globalThis_['Quill'] = QuillConstructor;
  }

  // Note: Module patching removed to avoid build errors

  console.log('✅ Quill initialized globally for ngx-quill');

  // Debug: Verify all assignments worked
  if (typeof globalWindow['Quill'] !== 'function') {
    console.error('❌ Failed to set window.Quill as constructor');
  } else {
    console.log('✅ Verified: window.Quill is available as constructor');
  }
}

// Export the Quill constructor for direct use
export const Quill = QuillConstructor;

// Export type alias for TypeScript
export type QuillInstance = InstanceType<typeof QuillConstructor>;

// Export commonly used Quill imports (lazy-loaded)
export const Parchment = Quill.import('parchment');
export const EmbedBlot = Quill.import('blots/embed');
export const InlineBlot = Quill.import('blots/inline');
export const BlockBlot = Quill.import('blots/block');

// Utility function to ensure Quill is ready (for manual initialization)
export function ensureQuillReady(): typeof QuillConstructor {
  // Quill is already initialized above, just return the constructor
  return QuillConstructor;
}

// Default export for convenience and webpack alias compatibility
export default Quill;

// Note: When used as webpack alias, this module will replace 'quill' imports
