/**
 * Centralized Quill Loader for Angular 19 ESM Compatibility
 * This module handles the proper loading and exposure of Quill for ngx-quill
 */

// Import Quill with Angular 19 ESM compatibility
import * as QuillNamespace from 'quill';

// Handle Angular 19's strict ESM - get the actual constructor
const QuillConstructor = (QuillNamespace as any).default || QuillNamespace;

// Verify we have the constructor
if (typeof QuillConstructor !== 'function') {
  console.error('❌ Quill import failed. Expected constructor, got:', typeof QuillConstructor);
  throw new Error('Quill is not a constructor - Angular 19 ESM compatibility issue');
}

// Comprehensive Quill initialization for ngx-quill compatibility
if (typeof window !== 'undefined') {
  // Set on window (primary method for ngx-quill)
  (window as any).Quill = QuillConstructor;

  // Set on globalThis for broader compatibility
  if (typeof globalThis !== 'undefined') {
    (globalThis as any).Quill = QuillConstructor;
  }

  // Patch the module system - some bundlers might cache the import
  try {
    // Force the module to export the constructor
    if (QuillNamespace && typeof QuillNamespace === 'object') {
      (QuillNamespace as any).default = QuillConstructor;
      // Also set it as a direct property
      (QuillNamespace as any).Quill = QuillConstructor;
    }
  } catch (e) {
    console.warn('Could not patch Quill module exports:', e);
  }

  console.log('✅ Quill initialized globally for ngx-quill');

  // Debug: Verify all assignments worked
  if (typeof (window as any).Quill !== 'function') {
    console.error('❌ Failed to set window.Quill as constructor');
  } else {
    console.log('✅ Verified: window.Quill is available as constructor');
  }
}

// Export the Quill constructor for direct use
export const Quill = QuillConstructor;

// Export type alias for TypeScript
export type QuillInstance = InstanceType<typeof QuillConstructor>;

// Export commonly used Quill imports (lazy-loaded)
export const Parchment = Quill.import('parchment');
export const EmbedBlot = Quill.import('blots/embed');
export const InlineBlot = Quill.import('blots/inline');
export const BlockBlot = Quill.import('blots/block');

// Utility function to ensure Quill is ready (for manual initialization)
export function ensureQuillReady(): typeof QuillConstructor {
  // Quill is already initialized above, just return the constructor
  return QuillConstructor;
}

// Default export for convenience
export default Quill;
