# Angular 19 ESM Quill Fix

## Root Cause Analysis

The "Quill is not a constructor" error was caused by **Angular 19's strict ESM handling**. Here's what changed:

### Angular 18 vs Angular 19 Import Behavior

**Angular 18 (Legacy Compatibility):**
```typescript
import Quill from 'quill';
// ✅ This would give you the constructor directly
// Angular 18 had "magic" interop that made default exports work
```

**Angular 19 (Strict ESM):**
```typescript
import Quill from 'quill';
// ❌ This gives you an object with a 'default' field
// Angular 19 dropped legacy compatibility hacks
```

### The Problem

When Angular 19 imports `quill`, it gets an object like:
```javascript
{
  default: [Function: Quill],  // The actual constructor
  // ... other exports
}
```

But ngx-quill expects `window.Quill` to be the constructor function directly.

## Solution Applied

### Updated Import Pattern

**Before (Angular 18 compatible):**
```typescript
import Quill from 'quill';
```

**After (Angular 19 ESM compatible):**
```typescript
// Import Quill with Angular 19 ESM compatibility
import * as QuillNamespace from 'quill';

// Handle Angular 19's strict ESM - get the actual constructor
const Quill = QuillNamespace.default || QuillNamespace;

// Ensure Quill is available globally for ngx-quill
if (typeof window !== 'undefined' && !((window as unknown as { Quill?: unknown }).Quill)) {
  (window as unknown as { Quill: unknown }).Quill = Quill;
}
```

### Files Updated

1. **libs/angular/unica-text-editor/src/lib/unica-text-editor/unica-text-editor.component.ts**
2. **libs/angular/unica-text-editor/src/lib/directives/text-autocomplete.directive.ts**
3. **libs/angular/unica-text-editor/src/lib/extentions/text-align.config.ts**
4. **libs/angular/unica-text-editor/src/lib/extentions/text-node.ts**
5. **libs/angular/unica-text-editor/src/lib/extentions/custom-link.ts**
6. **libs/angular/unica-text-editor/src/lib/helpers/quill-tags.helper.ts**
7. **libs/angular/unica-text-editor/src/lib/services/quill-tags.service.ts**

### TypeScript Type Fixes

Also fixed TypeScript errors by using proper type aliases:

```typescript
// Type alias for Quill instance
type QuillInstance = InstanceType<typeof Quill>;

// Usage in method signatures
onEditorCreated(editor: QuillInstance): void {
  // ...
}

// Usage in properties
private editorInstance: QuillInstance | null = null;
```

## Key Benefits

✅ **Angular 19 Compatible**: Works with strict ESM handling
✅ **Backward Compatible**: `|| QuillNamespace` fallback for edge cases
✅ **Type Safe**: Proper TypeScript typing throughout
✅ **Global Availability**: Ensures `window.Quill` is the constructor
✅ **ngx-quill Compatible**: Provides exactly what ngx-quill expects

## Testing

After applying this fix:

1. **Build the library:**
   ```bash
   npx nx build unica-text-editor
   ```

2. **Build applications:**
   ```bash
   npx nx build angular_lib
   npx nx build detect_ui
   ```

3. **Test in browser:**
   - ✅ No "Quill is not a constructor" errors
   - ✅ Text editor loads successfully
   - ✅ All Quill functionality works

## Technical Details

### Why This Works

1. **`import * as QuillNamespace from 'quill'`**: Gets the entire module object
2. **`QuillNamespace.default || QuillNamespace`**: Extracts the constructor whether it's in `.default` or the root
3. **Global assignment**: Makes the constructor available as `window.Quill` for ngx-quill
4. **Type safety**: Uses `InstanceType<typeof Quill>` for proper typing

### Angular 19 ESM Changes

Angular 19's strict ESM handling means:
- No more "magic" default export interop
- Packages must handle their own export structure
- More predictable but requires explicit handling

This fix ensures compatibility with Angular 19's modern ESM approach while maintaining functionality.
