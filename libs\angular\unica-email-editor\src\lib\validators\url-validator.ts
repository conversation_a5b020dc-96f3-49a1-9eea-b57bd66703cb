import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export interface UrlValidatorConfig {
  required?: boolean;          // default: true - whether field is required
  requiredPrefix?: string[];   // e.g. ['http://', 'https://', 'mailto:']
  checkSpaces?: boolean;       // default: true
  checkFullUrl?: boolean;      // default: true
}

export function urlValidator(config: UrlValidatorConfig = {}): ValidatorFn {
  const {
    required = true,
    requiredPrefix = ['http://', 'https://', 'mailto:'],
    checkSpaces = true,
    checkFullUrl = true,
  } = config;

  return (control: AbstractControl): ValidationErrors | null => {
    const value = control?.value?.trim();

    if (!value || value.length === 0) {
      return required ? { required: true } : null;
    }

    // Prefix check (if enabled and prefixes provided)
    if (requiredPrefix.length) {
      const hasPrefix = requiredPrefix.some((prefix) =>
        value.toLowerCase().startsWith(prefix.toLowerCase())
      );
      if (!hasPrefix) {
        return { invalidPrefix: true };
      }
    }

    // Spaces check (if enabled)
    if (checkSpaces && /\s/.test(value)) {
      return { containsSpace: true };
    }

    // Full URL validation
    if (checkFullUrl) {
      try {
        const parsed = new URL(value);
        const protocol = parsed.protocol.toLowerCase();
        if (((protocol === 'http:' || protocol === 'https:') && !parsed.hostname) || (protocol === 'mailto:' && (!parsed.pathname || parsed.pathname.length === 0))) {
          return { invalidUrl: true };
        }
      } catch {
        return { invalidUrl: true };
      }
    }

    return null;
  };
}