import { Injectable } from '@angular/core';
import { EmailCanvasService } from './email-canvas.service';
import { IRule } from '../config/email-common-elements';
import { BlocksOptions, BlockType, UnicaEmail, UnicaStructure } from '../config/email';

@Injectable()
export class AliasDuplicationService {

  constructor(private canvasService: EmailCanvasService) { }

  /**
   * Checks if the given alias (by name and ID) already exists in the current email JSON.
   * 
   * @param aliasNameInfo - The alias object containing ID and name to check for duplication
   * @returns true if a duplicate alias name exists with a different ID, false otherwise
   */
  hasDuplicateAlias(aliasNameInfo: { id: string; name: string }): boolean {

    const emailJson: UnicaEmail | undefined = this.canvasService.getEmailAsJson();
    const structures: UnicaStructure[] | undefined = emailJson?.structures ?? [];
    const aliasId: string = aliasNameInfo?.id ?? '';
    const aliasName: string = aliasNameInfo?.name ?? '';
    let isDuplicate = false;
    const parser = new DOMParser();

    if (!aliasName?.trim()) return false; // exit if aliasName is empty

    // Iterate over all blocks and check for duplicates in aliasNameInfo, rules, or inline HTML links
    structures.forEach(structure => {
      structure.elements.forEach(column => {
        column.forEach(element => {
          const type: BlockType | string = element.type;
          const options: BlocksOptions = element.options ?? {};
          const rules: IRule[] = 'rules' in options && Array.isArray(options.rules) ? options.rules : [];

          switch (type) {
            case 'button':
            case 'image':
            case 'preferences-unsubscribe-link': {
              if ('aliasNameInfo' in options) {
                const currentAlias = options.aliasNameInfo;
                if (currentAlias?.id && currentAlias.id !== aliasId && currentAlias.name === aliasName) {
                  isDuplicate = true;
                  break;
                }
              }
              if (!isDuplicate) {
                isDuplicate = this.checkRules(rules, aliasId, aliasName);
              }
              break;
            }

            case 'text': {
              if ('innerText' in element && typeof element.innerText === 'string') {
                isDuplicate = this.checkLinksInTextOrHtml(parser, element.innerText, aliasId, aliasName);
              }
              if (!isDuplicate) {
                isDuplicate = this.checkRules(rules, aliasId, aliasName);
              }
              break;
            }

            case 'html': {
              if ('innerHtml' in options && typeof options.innerHtml === 'string') {
                isDuplicate = this.checkLinksInTextOrHtml(parser, options.innerHtml, aliasId, aliasName);
              }
              break;
            }
            default:
              break;
          }
        });
      });
    });
    return isDuplicate;
  }

  /**
   * Checks if any rule in the given array uses the same alias name with a different ID.
   */
  checkRules(rules: IRule[], aliasId: string, aliasName: string): boolean {
    return rules.some((rule: IRule) => rule.id !== aliasId && rule.aliasName === aliasName)
  }

  /**
   * Parses the inner HTML or text and checks if any anchor (<a>) tag contains the same alias name
   * with a different ID via `data-id` and `data-alias` attributes.
   */
  checkLinksInTextOrHtml(parser: DOMParser, innerText: string | undefined | null, aliasId: string, aliasName: string): boolean {
    if (!innerText) return false;
    const links = parser.parseFromString(innerText, 'text/html').querySelectorAll('a');

    return Array.from(links).some(
      link =>
        link.hasAttribute('data-id') &&
        link.hasAttribute('data-alias') &&
        link.getAttribute('data-id') !== aliasId &&
        link.getAttribute('data-alias') === aliasName
    );
  }
}